import logging
from typing import Any

from flask import g, redirect, request, url_for
from flask.globals import current_app, session
from flask_appbuilder import expose
from flask_appbuilder.security.sqla.models import Role
from flask_appbuilder.security.views import AuthOAuthView
from flask_login import logout_user
from utils import get_env_variable

from superset.constants import ObjectiveRoleEnum
from superset.security import SupersetSecurityManager

logger = logging.getLogger(__name__)

# Roles allowed to access Superset
ALLOWED_ROLES = [
    ObjectiveRoleEnum.ROOT_ADMIN.value,
    ObjectiveRoleEnum.OPERATOR.value,
    ObjectiveRoleEnum.OFFICE_MANAGER.value,
    ObjectiveRoleEnum.ROOT_OFFICE_MANAGER.value,
    ObjectiveRoleEnum.ROOT_OPERATOR.value,
    ObjectiveRoleEnum.ACCOUNTANT.value,
    ObjectiveRoleEnum.FRANCHISEE_OFFICE_MANAGER.value,
    ObjectiveRoleEnum.DEVELOPER.value,
    ObjectiveRoleEnum.CONTROLLING_ADMIN.value,
    ObjectiveRoleEnum.CLIENTS_SUPPORT.value,
]

AUTH_URL_AZURE = "https://auth.dodois.com"
AUTH_URL_YANDEX = "https://auth.dodois.io"

# Azure OAuth provider configuration
DODO_OAUTH_PROVIDER_AZURE = {
    "name": "Other_Countries",
    "label": "Other Countries",
    "icon": "",
    "token_key": "access_token",
    "remote_app": {
        "client_id": get_env_variable("DODO_KEY"),
        "client_secret": get_env_variable("DODO_SECRET"),
        "api_base_url": AUTH_URL_AZURE,
        "client_kwargs": {
            "scope": "openid profile email roles superset",
            "code_challenge_method": "S256",
        },
        "request_token_url": None,
        "access_token_url": f"{AUTH_URL_AZURE}/connect/token",
        "authorize_url": f"{AUTH_URL_AZURE}/connect/authorize",
    },
}

# Yandex OAuth provider configuration
DODO_OAUTH_PROVIDER_YANDEX = {
    "name": "RU_KZ_BY_KG_UZ_TJ",
    "label": "Страны СНГ",
    "icon": "",
    "token_key": "access_token",
    "remote_app": {
        "client_id": get_env_variable("DODO_KEY"),
        "client_secret": get_env_variable("DODO_SECRET"),
        "api_base_url": AUTH_URL_YANDEX,
        "client_kwargs": {
            "scope": "openid profile email roles superset",
            "code_challenge_method": "S256",
        },
        "request_token_url": None,
        "access_token_url": f"{AUTH_URL_YANDEX}/connect/token",
        "authorize_url": f"{AUTH_URL_YANDEX}/connect/authorize",
    },
}

# Default roles for new user registration
FAB_AUTH_USER_REGISTRATION_ROLE = "Public"
DEFAULT_AUTH_USER_REGISTRATION_ROLES = [
    "readonly",
    "public_dashboards",
]


class DodoAuthOAuthView(AuthOAuthView):
    """
    Custom OAuth view for Dodo authentication.
    """

    login_template = "appbuilder/general/security/login_oauth.html"

    @expose("/login/")
    def dodo_login(self, provider: str | None = None):
        logger.info("Provider: %s", provider)
        if g.user is not None and g.user.is_authenticated:
            logger.info("Already authenticated %s", g.user)
            return redirect(self.appbuilder.get_url_for_index)

        if provider is None:
            return self.render_template(
                self.login_template,
                providers=self.appbuilder.sm.oauth_providers,
                title=self.title,
                appbuilder=self.appbuilder,
            )

    @expose("/logout/")
    def logout(self):
        logout_user()
        redirect_url = url_for(".login", _external=True)
        logger.info("Redirect url %s", redirect_url)
        # todo: Спросить у Ромы Букина, нужно ли разлогинивать именно через dodois auth
        # logout_url = self.appbuilder.app.config.get(
        #         "LOGOUT_REDIRECT_URL", self.appbuilder.get_url_for_index
        #     )
        # if "id_token" in session:
        #     id_token = session.pop("id_token")
        #     logout_url += f"&id_token_hint={id_token}"
        # logger.info("Logout url {0}".format(logout_url))
        return redirect(redirect_url)


class DodoSecurityManager(SupersetSecurityManager):
    """
    Custom security manager for Dodo authentication and user management.
    """

    authoauthview = DodoAuthOAuthView

    def load_user_jwt(self, _jwt_header: dict[str, Any], jwt_data: dict[str, Any]):
        identity = jwt_data[current_app.config.get("JWT_IDENTITY_CLAIM")]
        if isinstance(identity, int):
            user = self.load_user(identity)
        else:
            user = self.find_user(username=identity)

            # If the user is new, register them
            if (not user) and self.auth_user_registration:
                email, first_name, last_name = self._get_and_validate_user_info(
                    jwt_data, identity
                )

                user = self.add_user(
                    username=identity,
                    first_name=first_name,
                    last_name=last_name,
                    email=email,
                    role=self._get_default_roles(),
                )

                # Log new user registration metric using PrometheusStatsLogger
                try:
                    stats_logger = current_app.config["STATS_LOGGER"]
                    stats_logger.incr_new_user_registration(user_id=identity)
                except Exception as e:
                    logger.error(
                        "[dodo.py] Failed to log new user registration metric: %s", e
                    )

                # If user registration failed, go away
                if not user:
                    logger.error(
                        "[dodo.py] Error creating a new OAuth user %s", identity
                    )
                    return None

        # Set flask g.user to JWT user
        session["depid"] = jwt_data.get("depid")

        g.user = user
        return user

    def _get_and_validate_user_info(
        self, jwt_data: dict[str, Any], identity: str
    ) -> tuple:
        """
        Extract and validate user information from the JWT payload.
        """
        first_name = jwt_data.get("fname")
        last_name = jwt_data.get("lname")
        email = jwt_data.get("email")
        logger.info(
            "_get_and_validate_user_info with jwt_data = %s identity = %s",
            jwt_data,
            identity,
        )

        if not first_name:
            logger.warning("No first name provided for user %s", identity)
            raise ValueError(
                "No first name provided for fname:%s, lname:%s, email:%s, url:%s",
                first_name,
                last_name,
                email,
                request.url,
            )
        if not last_name:
            logger.warning("No last name provided for user %s", identity)
            raise ValueError(
                "No last name provided for fname:%s, lname:%s, email:%s, url:%s",
                first_name,
                last_name,
                email,
                request.url,
            )
        if email is None:
            logger.warning("No email provided for user %s", identity)  # TODO: fix this
            # raise ValueError("No email provided for fname:%s, lname:%s, email:%s, url:%s", first_name, last_name, email, request.url)
        return email, first_name, last_name

    def oauth_user_info(
        self, provider: str, response: dict[str, Any] | None = None
    ) -> dict[str, Any] | None:
        """
        Retrieve user information from the OAuth provider.
        """
        logger.info("OAuth2 provider: %s.", provider)

        if provider == "RU_KZ_BY_KG_UZ_TJ" or provider == "Other_Countries":
            userinfo_response = self.appbuilder.sm.oauth_remotes[provider].get(
                "connect/userinfo"
            )

            if userinfo_response.status_code != 200:
                logger.error("Failed to obtain user info: %s", userinfo_response.data)
                return None

            me: dict[str, Any] = userinfo_response.json()

            session["id_token"] = response.get("id_token")

            # Extract user details
            uuid = me["sub"].upper()
            userinfo = {
                "name": me["name"],
                "email": me["email"] if me.get("email") else uuid,
                "id": uuid,
                "username": uuid,
                "first_name": me["given_name"],
                "last_name": me["family_name"],
            }

            access = False
            roles = set()
            # Store session extras for department resolution
            try:
                dobr: list[str] | str = me["d:obr"]
                if isinstance(dobr, str):
                    dobr = [dobr]
                filtered_dobr = []
                for dobr_str in dobr:
                    uuid, dobr_type, role_str = dobr_str.split(":")
                    role = int(role_str)
                    roles.add(role)
                    if role in ALLOWED_ROLES:
                        access = True
                    dobr_str = f"{uuid}:{dobr_type}".upper()
                    if dobr_str not in filtered_dobr:
                        filtered_dobr.append(dobr_str)
                session["d:obr"] = filtered_dobr
            except Exception:  # pylint: disable=broad-exception-caught
                session.pop("d:obr", None)

            try:
                dmid = me.get("d:mid")
                session["d:mid"] = dmid.upper() if isinstance(dmid, str) else None
            except Exception:  # pylint: disable=broad-exception-caught
                session.pop("d:mid", None)

            if not access:
                logger.warning(
                    "User %s with d:obr %s is not allowed to access Superset",
                    userinfo["id"],
                    dobr,
                )
                raise ValueError("User is not allowed to access Superset")

            try:
                with current_app.app_context():
                    # pylint: disable=import-outside-toplevel
                    from superset.daos.objective_role import ObjectiveRoleDAO

                    if user := self.get_user_by_username(userinfo["username"]):
                        ObjectiveRoleDAO.update_user_roles(user, roles)
                    else:
                        logger.warning(
                            "Could not find user %s to save roles", userinfo["id"]
                        )
            except Exception as ex:  # pylint: disable=broad-exception-caught
                logger.error(
                    "Failed to save roles for user %s: %s", userinfo["id"], str(ex)
                )

            # Insert additional data into the database
            # with current_app.app_context():
            #     from superset.views.utils import insert_data_auth, insert_country
            #     try:
            #         insert_data_auth(me.__str__(), userinfo.get('username'))
            #         insert_country(me.get('d:cid'), userinfo.get('username')) # dodo add 34269608
            #     except Exception as e:
            #         logger.warning("cant insert userinfo from auth and country")
            #         logger.warning(e)

            return userinfo

        # Fallback to default implementation
        userinfo = super().get_oauth_user_info(provider, response)

        if "email" in userinfo:
            userinfo["username"] = userinfo["email"]

        # Insert additional data into the database
        # with current_app.app_context():
        #     from superset.views.utils import insert_data_auth, insert_country
        #     try:
        #         insert_data_auth("data_auth", userinfo.get('username'))
        #         insert_country(643, userinfo.get('username')) # Russian Federation dodo add 34269608
        #     except Exception as e:
        #         logger.warning("Can't insert userinfo from auth and country")
        #         logger.warning(e)

        return userinfo

    def _oauth_calculate_user_roles(self, userinfo: dict[str, Any]) -> list[Role]:
        """
        Calculate roles for the user based on OAuth information.
        """
        user_roles: list[Role] = super()._oauth_calculate_user_roles(userinfo)

        if (
            len(user_roles) > 1
            and user_roles[0].name != FAB_AUTH_USER_REGISTRATION_ROLE
        ):
            return user_roles

        default_roles = self._get_default_roles()

        if not default_roles:
            raise ValueError(
                "Can't apply default roles because default roles are empty array"
            )

        logger.warning(
            'Set default roles %s instead of "%s" for user with email "%s"',
            default_roles,
            FAB_AUTH_USER_REGISTRATION_ROLE,
            userinfo["email"],
        )

        return default_roles

    def _get_default_roles(self) -> list[Role]:
        """
        Retrieve default roles for new user registration.
        """
        all_roles = {role.name: role for role in self.get_all_roles()}
        default_roles = []

        for role_name in DEFAULT_AUTH_USER_REGISTRATION_ROLES:
            if role_name in all_roles:
                default_roles.append(all_roles[role_name])
            else:
                logger.warning('Role "%s" was not found in Superset Roles', role_name)

        return default_roles


class DodoDevSecurityManager(DodoSecurityManager):
    """
    Development-specific security manager for Dodo authentication.
    """

    authoauthview = DodoAuthOAuthView
