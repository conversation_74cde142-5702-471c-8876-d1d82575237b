// DODO was here
import { Children, cloneElement } from 'react';

export function recurseReactClone(children, type, propExtender) {
  /**
   * Clones a React component's children, and injects new props
   * where the type specified is matched.
   */
  return Children.map(children, child => {
    let newChild = child;
    if (child && child.type.name === type.name) {
      newChild = cloneElement(child, propExtender(child));
    }
    if (newChild && newChild.props.children) {
      newChild = cloneElement(newChild, {
        children: recurseReactClone(
          newChild.props.children,
          type,
          propExtender,
        ),
      });
    }
    return newChild;
  });
}

// DODO added 57936197
export function isCertifiedByGA(extra) {
  const { certification } = JSON.parse(extra || '{}') || {};
  return certification?.certified_by === 'Superset Certification Lab';
}

// DODO added 57936197
export function formatSqlString(raw) {
  return (
    raw
      // убрать внешние кавычки (если строка заключена в "")
      .replace(/^"(.*)"$/, '$1')

      // заменить \n на реальные переносы
      .replace(/\\n/g, '\n')

      // заменить \t на табы
      .replace(/\\t/g, '\t')

      // убрать двойной escaping типа \\n → \n
      .replace(/\\\\n/g, '\\n')

      // не трогаем пробелы, запятые, отступы
      .trim()
  );
}


