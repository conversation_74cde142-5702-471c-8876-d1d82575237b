// DODO was here
import { useCallback, useEffect, useState } from 'react';
import { Link, useParams } from 'react-router-dom';
import { addAlpha, styled, SupersetClient, t } from '@superset-ui/core';
import { Tabs } from 'antd';
import SubMenu from 'src/features/home/<USER>';
import Loading from 'src/components/Loading';
import { useToasts } from 'src/components/MessageToasts/withToasts';
import Button from 'src/components/Button';
import rison from 'rison';
import { InfoList, InfoItem } from 'src/DodoExtensions/components/InfoList';
import AssetList from './components/AssetList';
import ChangeOwnerModal from './components/ChangeOwnerModal';
import ChangeDodoRoleModal from './components/ChangeDodoRoleModal';

const { TabPane } = Tabs;

const PageWrapper = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.gridUnit * 4}px;
  padding: ${({ theme }) => theme.gridUnit * 4}px;
  height: calc(100vh - 120px);
`;

const Sidebar = styled.div`
  width: 320px;
  flex-shrink: 0;
  background: ${({ theme }) => theme.colors.grayscale.light5};
  border: 1px solid ${({ theme }) => theme.colors.grayscale.light2};
  border-radius: ${({ theme }) => theme.gridUnit}px;
  overflow-y: auto;
  height: fit-content;
  max-height: 100%;
`;

const SidebarHeader = styled.div`
  padding: ${({ theme }) => theme.gridUnit * 5}px
    ${({ theme }) => theme.gridUnit * 4}px;
  border-bottom: 1px solid ${({ theme }) => theme.colors.grayscale.light2};
`;

const UserName = styled.h2`
  margin: 0 0 ${({ theme }) => theme.gridUnit * 2}px 0;
  font-size: 20px;
  font-weight: ${({ theme }) => theme.typography.weights.bold};
  color: ${({ theme }) => theme.colors.grayscale.dark2};
  line-height: 1.3;
`;

const StatusBadge = styled.span<{ active: boolean }>`
  display: inline-flex;
  align-items: center;
  padding: ${({ theme }) => theme.gridUnit}px
    ${({ theme }) => theme.gridUnit * 2.5}px;
  border-radius: ${({ theme }) => theme.gridUnit * 3}px;
  font-size: ${({ theme }) => theme.typography.sizes.s}px;
  font-weight: ${({ theme }) => theme.typography.weights.medium};
  background: ${({ theme, active }) =>
    active ? theme.colors.success.light2 : theme.colors.error.light2};
  color: ${({ theme, active }) =>
    active ? theme.colors.success.dark1 : theme.colors.error.dark1};
`;

const MainContent = styled.div`
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  background: ${({ theme }) => theme.colors.grayscale.light5};
  border: 1px solid ${({ theme }) => theme.colors.grayscale.light2};
  border-radius: ${({ theme }) => theme.gridUnit}px;
  overflow: hidden;
`;

const TabsWrapper = styled.div`
  padding: ${({ theme }) => theme.gridUnit * 4}px;
`;

const StickyActionBar = styled.div<{ visible: boolean }>`
  position: fixed;
  bottom: ${({ theme }) => theme.gridUnit * 4}px;
  right: ${({ theme }) => theme.gridUnit * 4}px;
  z-index: 100;
  transition: all 0.3s ease;
  opacity: ${({ visible }) => (visible ? 1 : 0)};
  transform: translateY(${({ visible }) => (visible ? 0 : '20px')});
  pointer-events: ${({ visible }) => (visible ? 'auto' : 'none')};
`;

const ActionCard = styled.div`
  background: ${({ theme }) => theme.colors.grayscale.light5};
  border: 1px solid ${({ theme }) => theme.colors.grayscale.light2};
  border-radius: ${({ theme }) => theme.gridUnit}px;
  padding: ${({ theme }) => theme.gridUnit * 3}px
    ${({ theme }) => theme.gridUnit * 4}px;
  box-shadow: 0 4px 12px
    ${({ theme }) => addAlpha(theme.colors.grayscale.dark2, 0.05)};
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.gridUnit * 3}px;
`;

const ActionInfo = styled.div`
  .count {
    font-size: ${({ theme }) => theme.typography.sizes.m}px;
    font-weight: ${({ theme }) => theme.typography.weights.bold};
    color: ${({ theme }) => theme.colors.grayscale.dark2};
    margin-bottom: ${({ theme }) => theme.gridUnit / 2}px;
  }

  .label {
    font-size: ${({ theme }) => theme.typography.sizes.s}px;
    color: ${({ theme }) => theme.colors.grayscale.base};
  }
`;

interface UserData {
  id: number;
  username: string;
  first_name: string;
  last_name: string;
  email: string;
  is_active: boolean;
  created_on: string;
  last_login: string;
  login_count: number;
  user_info?: {
    dodo_role?: string;
    country_name?: string;
  };
}

interface SelectedAssets {
  dashboards: number[];
  charts: number[];
  datasets: number[];
}

const SingleUser = () => {
  const { id } = useParams<{ id: string }>();
  const [loading, setLoading] = useState(true);
  const [user, setUser] = useState<UserData | null>(null);
  const [selectedAssets, setSelectedAssets] = useState<SelectedAssets>({
    dashboards: [],
    charts: [],
    datasets: [],
  });
  const [showChangeOwnerModal, setShowChangeOwnerModal] = useState(false);
  const [showChangeDodoRoleModal, setShowChangeDodoRoleModal] = useState(false);
  const { addDangerToast, addSuccessToast } = useToasts();

  const fetchUser = useCallback(async () => {
    if (!id) return;

    try {
      setLoading(true);
      const response = await SupersetClient.get({
        endpoint: `/api/v1/dodo_user/${id}?q=${rison.encode({
          columns: [
            'id',
            'username',
            'first_name',
            'last_name',
            'email',
            'is_active',
            'created_on',
            'last_login',
            'login_count',
            'user_info.country_name',
            'user_info.dodo_role',
          ],
        })}`,
      });
      setUser(response.json.result);
    } catch (error) {
      addDangerToast(t('Error fetching user data'));
    } finally {
      setLoading(false);
    }
  }, [id, addDangerToast]);

  useEffect(() => {
    fetchUser();
  }, [fetchUser]);

  const handleAssetSelection = useCallback(
    (assetType: keyof SelectedAssets, selectedIds: number[]) => {
      setSelectedAssets(prev => ({
        ...prev,
        [assetType]: selectedIds,
      }));
    },
    [],
  );

  const handleChangeOwner = () => {
    const totalSelected =
      selectedAssets.dashboards.length +
      selectedAssets.charts.length +
      selectedAssets.datasets.length;

    if (totalSelected === 0) {
      addDangerToast(t('Please select at least one asset to change ownership'));
      return;
    }

    setShowChangeOwnerModal(true);
  };

  const handleOwnershipChanged = () => {
    setShowChangeOwnerModal(false);
    setSelectedAssets({
      dashboards: [],
      charts: [],
      datasets: [],
    });
    addSuccessToast(t('Ownership changed successfully'));
  };

  if (loading) {
    return <Loading position="floating" />;
  }

  if (!user) {
    return (
      <PageWrapper>
        <h2>{t('User not found')}</h2>
      </PageWrapper>
    );
  }

  const totalSelected =
    selectedAssets.dashboards.length +
    selectedAssets.charts.length +
    selectedAssets.datasets.length;

  return (
    <>
      <SubMenu name={t('User Profile')} />
      <PageWrapper>
        <Sidebar>
          <SidebarHeader>
            <UserName>
              {user.first_name} {user.last_name}
            </UserName>
            <StatusBadge active={user.is_active}>
              {user.is_active ? t('Active') : t('Inactive')}
            </StatusBadge>
          </SidebarHeader>

          <InfoList>
            <InfoItem>
              <div className="label">{t('User ID')}</div>
              <div className="value">
                <Link to={`/users/list/?_flt_0_id=${user.id}`} target="_blank">
                  {user.id}
                </Link>
              </div>
            </InfoItem>

            <InfoItem>
              <div className="label">{t('Username')}</div>
              <div className="value">{user.username}</div>
            </InfoItem>

            <InfoItem>
              <div className="label">{t('Email Address')}</div>
              <div className="value">{user.email}</div>
            </InfoItem>

            <InfoItem>
              <div className="label">{t('DODO Role')}</div>
              <div
                className="value-clickable"
                onClick={() => setShowChangeDodoRoleModal(true)}
                role="button"
                tabIndex={0}
                onKeyDown={e => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    setShowChangeDodoRoleModal(true);
                  }
                }}
              >
                {user.user_info?.dodo_role || t('Not assigned')}
              </div>
            </InfoItem>

            {user.user_info?.country_name && (
              <InfoItem>
                <div className="label">{t('Country')}</div>
                <div className="value">{user.user_info.country_name}</div>
              </InfoItem>
            )}

            <InfoItem>
              <div className="label">{t('Login Count')}</div>
              <div className="value">{user.login_count || 0}</div>
            </InfoItem>

            <InfoItem>
              <div className="label">{t('Created On')}</div>
              <div className="value">
                {user.created_on
                  ? new Date(user.created_on).toLocaleString('en-GB', {
                      year: 'numeric',
                      month: 'short',
                      day: 'numeric',
                      hour: '2-digit',
                      minute: '2-digit',
                    })
                  : '-'}
              </div>
            </InfoItem>

            <InfoItem>
              <div className="label">{t('Last Login')}</div>
              <div className="value">
                {user.last_login
                  ? new Date(user.last_login).toLocaleString('en-GB', {
                      year: 'numeric',
                      month: 'short',
                      day: 'numeric',
                      hour: '2-digit',
                      minute: '2-digit',
                    })
                  : t('Never')}
              </div>
            </InfoItem>
          </InfoList>
        </Sidebar>

        <MainContent>
          <TabsWrapper>
            <Tabs defaultActiveKey="dashboards">
              <TabPane tab={t('Dashboards')} key="dashboards">
                <AssetList
                  userId={parseInt(id!, 10)}
                  assetType="dashboard"
                  onSelectionChange={ids =>
                    handleAssetSelection('dashboards', ids)
                  }
                />
              </TabPane>
              <TabPane tab={t('Charts')} key="charts">
                <AssetList
                  userId={parseInt(id!, 10)}
                  assetType="chart"
                  onSelectionChange={ids => handleAssetSelection('charts', ids)}
                />
              </TabPane>
              <TabPane tab={t('Datasets')} key="datasets">
                <AssetList
                  userId={parseInt(id!, 10)}
                  assetType="dataset"
                  onSelectionChange={ids =>
                    handleAssetSelection('datasets', ids)
                  }
                />
              </TabPane>
            </Tabs>
          </TabsWrapper>
        </MainContent>

        <StickyActionBar visible={totalSelected > 0}>
          <ActionCard>
            <ActionInfo>
              <div className="count">
                {totalSelected} {t('selected')}
              </div>
              <div className="label">{t('Assets ready for transfer')}</div>
            </ActionInfo>
            <Button buttonStyle="primary" onClick={handleChangeOwner}>
              {t('Change Owner')}
            </Button>
          </ActionCard>
        </StickyActionBar>
      </PageWrapper>

      {showChangeOwnerModal && user && (
        <ChangeOwnerModal
          sourceUserId={user.id}
          sourceUserName={`${user.first_name} ${user.last_name}`}
          selectedAssets={selectedAssets}
          onClose={() => setShowChangeOwnerModal(false)}
          onSuccess={handleOwnershipChanged}
        />
      )}

      {showChangeDodoRoleModal && user && (
        <ChangeDodoRoleModal
          userId={user.id}
          userName={`${user.first_name} ${user.last_name}`}
          userEmail={user.email}
          currentDodoRole={user.user_info?.dodo_role}
          onClose={() => setShowChangeDodoRoleModal(false)}
          onSuccess={fetchUser}
        />
      )}
    </>
  );
};

export default SingleUser;
