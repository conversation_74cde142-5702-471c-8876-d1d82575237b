// DODO was here
import { useMemo } from 'react';
import { useSelector } from 'react-redux';
import {
  css,
  Currency,
  ensureIsArray,
  getCurrencySymbol,
  styled,
  t,
} from '@superset-ui/core';
import { CSSObject } from '@emotion/react';
import { Select } from 'src/components';
import { ViewState } from 'src/views/types';
import { SelectProps } from 'src/components/Select/types';
import ControlHeader from '../../ControlHeader';

interface CurrencyControlPropsDodoExtended {
  disabled?: boolean; // DODO added 57936197
}
export interface CurrencyControlProps extends CurrencyControlPropsDodoExtended {
  onChange: (currency: Partial<Currency>) => void;
  value?: Partial<Currency>;
  symbolSelectOverrideProps?: Partial<SelectProps>;
  currencySelectOverrideProps?: Partial<SelectProps>;
  symbolSelectAdditionalStyles?: CSSObject;
  currencySelectAdditionalStyles?: CSSObject;
}

const CurrencyControlContainer = styled.div`
  ${({ theme }) => css`
    display: flex;
    align-items: center;

    & > :first-child {
      margin-right: ${theme.gridUnit * 4}px;
      min-width: 0;
      flex: 1;
    }

    & > :nth-child(2) {
      min-width: 0;
      flex: 1;
    }
  `}
`;

export const CURRENCY_SYMBOL_POSITION_OPTIONS = [
  { value: 'prefix', label: t('Prefix') },
  { value: 'suffix', label: t('Suffix') },
];

export const CurrencyControl = ({
  onChange,
  value: currency = {},
  symbolSelectOverrideProps = {},
  currencySelectOverrideProps = {},
  symbolSelectAdditionalStyles,
  currencySelectAdditionalStyles,
  disabled, // DODO added 57936197
  ...props
}: CurrencyControlProps) => {
  const currencies = useSelector<ViewState, string[]>(
    state => state.common?.currencies,
  );
  const currenciesOptions = useMemo(
    () =>
      ensureIsArray(currencies).map(currencyCode => ({
        value: currencyCode,
        label: `${getCurrencySymbol({
          symbol: currencyCode,
        })} (${currencyCode})`,
      })),
    [currencies],
  );
  return (
    <>
      <ControlHeader {...props} />
      <CurrencyControlContainer
        css={css`
          & > :first-child {
            ${symbolSelectAdditionalStyles};
          }
          & > :nth-child(2) {
            ${currencySelectAdditionalStyles};
          }
        `}
        className="currency-control-container"
      >
        <Select
          ariaLabel={t('Currency prefix or suffix')}
          options={CURRENCY_SYMBOL_POSITION_OPTIONS}
          placeholder={t('Prefix or suffix')}
          onChange={(symbolPosition: string) => {
            onChange({ ...currency, symbolPosition });
          }}
          onClear={() => onChange({ ...currency, symbolPosition: undefined })}
          value={currency?.symbolPosition}
          allowClear
          disabled={disabled} // DODO added 57936197
          {...symbolSelectOverrideProps}
        />
        <Select
          ariaLabel={t('Currency symbol')}
          options={currenciesOptions}
          placeholder={t('Currency')}
          onChange={(symbol: string) => {
            onChange({ ...currency, symbol });
          }}
          onClear={() => onChange({ ...currency, symbol: undefined })}
          value={currency?.symbol}
          allowClear
          allowNewOptions
          disabled={disabled} // DODO added 57936197
          {...currencySelectOverrideProps}
        />
      </CurrencyControlContainer>
    </>
  );
};
